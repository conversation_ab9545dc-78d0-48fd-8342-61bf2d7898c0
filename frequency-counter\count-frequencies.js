/**
 * Count Frequencies Problem
 * Given an array of integers, return an object where the keys are the numbers 
 * and the values are how often they appear.
 */

function countFrequencies(arr) {
    const freqMap = {};

    for (const num of arr) {
        freqMap[num] = (freqMap[num] || 0) + 1;
    }

    return freqMap;
}

// Test cases
console.log('Testing countFrequencies function:');
console.log('Input: [1, 2, 2, 3, 3, 3] -> Output:', countFrequencies([1, 2, 2, 3, 3, 3])); // Expected: {1:1, 2:2, 3:3}
console.log('Input: [1, 1, 1, 1] -> Output:', countFrequencies([1, 1, 1, 1])); // Expected: {1:4}
console.log('Input: [1, 2, 3, 4, 5] -> Output:', countFrequencies([1, 2, 3, 4, 5])); // Expected: {1:1, 2:1, 3:1, 4:1, 5:1}
console.log('Input: [] -> Output:', countFrequencies([])); // Expected: {}
console.log('Input: [0, -1, -1, 0, 5] -> Output:', countFrequencies([0, -1, -1, 0, 5])); // Expected: {0:2, -1:2, 5:1}

module.exports = countFrequencies;
