const { groupAnagrams, groupAnagramsFrequency } = require('./group-anagrams');

/**
 * Test suite for groupAnagrams functions
 */

function runTests() {
    console.log('Running tests for groupAnagrams functions...\n');
    
    let testsPassed = 0;
    let totalTests = 0;

    function test(description, input, expectedGroups, testFunction = groupAnagrams) {
        totalTests++;
        const result = testFunction(input);
        
        // Sort both result and expected for comparison (since order doesn't matter)
        const sortedResult = result.map(group => group.sort()).sort();
        const sortedExpected = expectedGroups.map(group => group.sort()).sort();
        
        const passed = JSON.stringify(sortedResult) === JSON.stringify(sortedExpected);
        
        console.log(`Test ${totalTests}: ${description}`);
        console.log(`  Input: [${input.map(s => `"${s}"`).join(', ')}]`);
        console.log(`  Expected groups: ${JSON.stringify(expectedGroups)}`);
        console.log(`  Got groups: ${JSON.stringify(result)}`);
        console.log(`  Status: ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);
        
        if (passed) testsPassed++;
    }

    // Test cases for main function
    console.log('=== Testing groupAnagrams (sorting approach) ===');
    
    test('Basic anagram grouping', 
         ["eat","tea","tan","ate","nat","bat"], 
         [["eat","tea","ate"],["tan","nat"],["bat"]]);
    
    test('Empty array', [], []);
    
    test('Single string', ["a"], [["a"]]);
    
    test('No anagrams', 
         ["abc", "def", "ghi"], 
         [["abc"], ["def"], ["ghi"]]);
    
    test('All anagrams', 
         ["abc", "bca", "cab"], 
         [["abc", "bca", "cab"]]);
    
    test('Mixed case and duplicates', 
         ["listen", "silent", "hello", "world", "enlist"], 
         [["listen", "silent", "enlist"], ["hello"], ["world"]]);
    
    test('Single character strings', 
         ["a", "b", "a", "c", "b"], 
         [["a", "a"], ["b", "b"], ["c"]]);

    // Test cases for frequency approach
    console.log('\n=== Testing groupAnagramsFrequency (frequency approach) ===');
    
    test('Basic anagram grouping (frequency)', 
         ["eat","tea","tan","ate","nat","bat"], 
         [["eat","tea","ate"],["tan","nat"],["bat"]], 
         groupAnagramsFrequency);
    
    test('Empty array (frequency)', [], [], groupAnagramsFrequency);
    
    test('All anagrams (frequency)', 
         ["abc", "bca", "cab"], 
         [["abc", "bca", "cab"]], 
         groupAnagramsFrequency);

    console.log(`\nTest Results: ${testsPassed}/${totalTests} tests passed`);
    
    if (testsPassed === totalTests) {
        console.log('🎉 All tests passed!');
    } else {
        console.log('❌ Some tests failed. Please review the implementation.');
    }
}

// Performance comparison
function performanceTest() {
    console.log('\n=== Performance Comparison ===');
    
    // Generate test data
    const testData = [];
    const words = ['listen', 'silent', 'enlist', 'hello', 'world', 'abc', 'bca', 'cab'];
    for (let i = 0; i < 1000; i++) {
        testData.push(words[i % words.length] + i);
    }
    
    console.log(`Testing with ${testData.length} strings...`);
    
    // Test sorting approach
    const start1 = Date.now();
    groupAnagrams(testData);
    const time1 = Date.now() - start1;
    
    // Test frequency approach
    const start2 = Date.now();
    groupAnagramsFrequency(testData);
    const time2 = Date.now() - start2;
    
    console.log(`Sorting approach: ${time1}ms`);
    console.log(`Frequency approach: ${time2}ms`);
    console.log(`Winner: ${time1 < time2 ? 'Sorting' : 'Frequency'} approach`);
}

// Run all tests
runTests();
performanceTest();
