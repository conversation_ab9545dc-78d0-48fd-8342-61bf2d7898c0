#include <bits/stdc++.h>

using namespace std;

/*
 * Problem: Find Removable Indices
 * 
 * Given two strings str1 and str2, where str1 contains exactly one character more than str2,
 * find the indices of characters in str1 that can be removed to make str1 equal to str2.
 * Return the array of indices in increasing order. If not possible, return [-1].
 * 
 * Time Complexity: O(n) where n is the length of str1
 * Space Complexity: O(1) excluding the result array
 * 
 * Approach:
 * 1. Use two pointers to traverse both strings
 * 2. When characters match, advance both pointers
 * 3. When they don't match, we found a potential removable character
 * 4. Check if removing this character allows the rest to match
 * 5. Collect all valid removable indices
 */

vector<int> getRemovableIndices(string str1, string str2) {
    int n1 = str1.length();
    int n2 = str2.length();

    // Edge case: str1 should be exactly 1 character longer than str2
    if (n1 != n2 + 1) {
        return {-1};
    }

    vector<int> removableIndices;

    // Try removing each character from str1 and check if it matches str2
    for (int removeIndex = 0; removeIndex < n1; removeIndex++) {
        string temp = str1;
        temp.erase(removeIndex, 1); // Remove character at removeIndex

        if (temp == str2) {
            removableIndices.push_back(removeIndex);
        }
    }

    // If no valid removable indices found, return [-1]
    if (removableIndices.empty()) {
        return {-1};
    }

    return removableIndices;
}



int main()
{
    string str1;
    getline(cin, str1);

    string str2;
    getline(cin, str2);

    vector<int> result = getRemovableIndices(str1, str2);

    for (size_t i = 0; i < result.size(); i++) {
        cout << result[i];

        if (i != result.size() - 1) {
            cout << "\n";
        }
    }

    cout << "\n";

    return 0;
}
