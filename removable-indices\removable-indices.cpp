#include <bits/stdc++.h>

using namespace std;

/*
 * Problem: Find Removable Indices
 * 
 * Given two strings str1 and str2, where str1 contains exactly one character more than str2,
 * find the indices of characters in str1 that can be removed to make str1 equal to str2.
 * Return the array of indices in increasing order. If not possible, return [-1].
 * 
 * Time Complexity: O(n) where n is the length of str1
 * Space Complexity: O(1) excluding the result array
 * 
 * Approach:
 * 1. Use two pointers to traverse both strings
 * 2. When characters match, advance both pointers
 * 3. When they don't match, we found a potential removable character
 * 4. Check if removing this character allows the rest to match
 * 5. Collect all valid removable indices
 */

vector<int> getRemovableIndices(string str1, string str2) {
    int n1 = str1.length();
    int n2 = str2.length();
    
    // Edge case: str1 should be exactly 1 character longer than str2
    if (n1 != n2 + 1) {
        return {-1};
    }
    
    vector<int> removableIndices;
    
    // Try removing each character from str1 and check if it matches str2
    for (int removeIndex = 0; removeIndex < n1; removeIndex++) {
        int i = 0; // pointer for str1
        int j = 0; // pointer for str2
        bool canMatch = true;
        
        // Traverse str1 while skipping the character at removeIndex
        while (i < n1 && j < n2) {
            if (i == removeIndex) {
                // Skip the character we're trying to remove
                i++;
                continue;
            }
            
            if (str1[i] == str2[j]) {
                // Characters match, advance both pointers
                i++;
                j++;
            } else {
                // Characters don't match, this removal won't work
                canMatch = false;
                break;
            }
        }
        
        // Check if we successfully matched all characters in str2
        // and we've processed all characters in str1 (considering the removal)
        if (canMatch && j == n2 && (i == n1 || (i == n1 - 1 && removeIndex == n1 - 1))) {
            removableIndices.push_back(removeIndex);
        }
    }
    
    // If no valid removable indices found, return [-1]
    if (removableIndices.empty()) {
        return {-1};
    }
    
    return removableIndices;
}

// Alternative optimized approach using prefix and suffix matching
vector<int> getRemovableIndicesOptimized(string str1, string str2) {
    int n1 = str1.length();
    int n2 = str2.length();
    
    if (n1 != n2 + 1) {
        return {-1};
    }
    
    // Find the longest common prefix
    int prefixLen = 0;
    while (prefixLen < n2 && str1[prefixLen] == str2[prefixLen]) {
        prefixLen++;
    }
    
    // Find the longest common suffix
    int suffixLen = 0;
    while (suffixLen < n2 - prefixLen && 
           str1[n1 - 1 - suffixLen] == str2[n2 - 1 - suffixLen]) {
        suffixLen++;
    }
    
    vector<int> result;
    
    // The removable character must be in the "gap" between prefix and suffix
    // All characters in this gap can potentially be removed
    int gapStart = prefixLen;
    int gapEnd = n1 - suffixLen - 1;
    
    if (gapStart <= gapEnd) {
        for (int i = gapStart; i <= gapEnd; i++) {
            result.push_back(i);
        }
    }
    
    return result.empty() ? vector<int>{-1} : result;
}

int main()
{
    // For interactive input (original requirement)
    /*
    string str1;
    getline(cin, str1);

    string str2;
    getline(cin, str2);

    vector<int> result = getRemovableIndices(str1, str2);

    for (size_t i = 0; i < result.size(); i++) {
        cout << result[i];

        if (i != result.size() - 1) {
            cout << "\n";
        }
    }

    cout << "\n";
    */
    
    // Test cases for verification
    cout << "\n=== Test Cases ===" << endl;
    
    // Test case 1: Example from problem
    string test1_str1 = "abdgggda";
    string test1_str2 = "abdggda";
    vector<int> test1_result = getRemovableIndices(test1_str1, test1_str2);
    cout << "Test 1: str1=\"" << test1_str1 << "\", str2=\"" << test1_str2 << "\"" << endl;
    cout << "Result: ";
    for (int idx : test1_result) {
        cout << idx << " ";
    }
    cout << endl;
    cout << "Expected: 3 4 5" << endl;
    
    // Test case 2: Only one possible removal
    string test2_str1 = "abc";
    string test2_str2 = "ac";
    vector<int> test2_result = getRemovableIndices(test2_str1, test2_str2);
    cout << "\nTest 2: str1=\"" << test2_str1 << "\", str2=\"" << test2_str2 << "\"" << endl;
    cout << "Result: ";
    for (int idx : test2_result) {
        cout << idx << " ";
    }
    cout << endl;
    cout << "Expected: 1" << endl;
    
    // Test case 3: Impossible case
    string test3_str1 = "abc";
    string test3_str2 = "def";
    vector<int> test3_result = getRemovableIndices(test3_str1, test3_str2);
    cout << "\nTest 3: str1=\"" << test3_str1 << "\", str2=\"" << test3_str2 << "\"" << endl;
    cout << "Result: ";
    for (int idx : test3_result) {
        cout << idx << " ";
    }
    cout << endl;
    cout << "Expected: -1" << endl;
    
    // Test optimized version
    cout << "\n=== Optimized Version Test ===" << endl;
    vector<int> opt_result = getRemovableIndicesOptimized(test1_str1, test1_str2);
    cout << "Optimized result for test 1: ";
    for (int idx : opt_result) {
        cout << idx << " ";
    }
    cout << endl;

    return 0;
}

/*
 * Complexity Analysis:
 * 
 * Approach 1 (Brute Force):
 * - Time: O(n²) where n is length of str1 (try removing each character)
 * - Space: O(1) excluding result array
 * 
 * Approach 2 (Optimized with Prefix/Suffix):
 * - Time: O(n) where n is length of str1
 * - Space: O(1) excluding result array
 * 
 * Key Insights:
 * 1. The removable characters form a contiguous segment
 * 2. This segment is between the longest common prefix and suffix
 * 3. All characters in this segment can be removed to get str2
 * 4. If no such segment exists, return [-1]
 */
