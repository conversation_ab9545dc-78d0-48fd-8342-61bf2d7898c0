/**
 * Count Frequencies Problem
 * Given an array of integers, return an object where the keys are the numbers 
 * and the values are how often they appear.
 */

function countFrequencies(arr) {
    const freqMap = {};

    for (const num of arr) {
        freqMap[num] = (freqMap[num] || 0) + 1;
    }

    return freqMap;
}

// Test
console.log(countFrequencies([1, 2, 2, 3, 3, 3])); // {1:1, 2:2, 3:3}
