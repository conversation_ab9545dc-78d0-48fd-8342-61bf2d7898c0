/**
 * Group Anagrams Problem
 * Given an array of strings strs, group the anagrams together. 
 * You can return the answer in any order.
 * 
 * Time Complexity: O(n * k * log k) 
 * - n = number of strings in the array
 * - k = maximum length of a string
 * - log k factor comes from sorting each string's characters
 * 
 * Space Complexity: O(n * k)
 * - n * k space for storing all strings in the result
 * - Additional space for the hash map to group anagrams
 * 
 * Approach: Use sorted characters as a unique key for each anagram group
 * - Anagrams will have identical sorted character sequences
 * - Group strings by their sorted character keys using a Map
 */

function groupAnagrams(strs) {
    // Use Map for better performance than plain object
    const anagramGroups = new Map();
    
    for (const str of strs) {
        // Create unique key by sorting characters
        // Anagrams will have the same sorted key: "eat" -> "aet", "tea" -> "aet"
        const sortedKey = str.split('').sort().join('');
        
        // Initialize group if it doesn't exist
        if (!anagramGroups.has(sortedKey)) {
            anagramGroups.set(sortedKey, []);
        }
        
        // Add current string to its anagram group
        anagramGroups.get(sortedKey).push(str);
    }
    
    // Convert Map values to array and return
    return Array.from(anagramGroups.values());
}

// Test cases
console.log('=== Group Anagrams Test Cases ===');

// Main test case
const input1 = ["eat","tea","tan","ate","nat","bat"];
const result1 = groupAnagrams(input1);
console.log('Input:', input1);
console.log('Output:', result1);
console.log('Expected: [["eat","tea","ate"],["tan","nat"],["bat"]]');
console.log('✅ Test 1 passed:', JSON.stringify(result1.map(group => group.sort()).sort()) === 
    JSON.stringify([["ate","eat","tea"],["bat"],["nat","tan"]]));

// Edge cases
console.log('\n=== Edge Cases ===');

// Empty array
console.log('Empty array:', groupAnagrams([]));

// Single string
console.log('Single string:', groupAnagrams(["hello"]));

// No anagrams
console.log('No anagrams:', groupAnagrams(["abc", "def", "ghi"]));

// All anagrams
console.log('All anagrams:', groupAnagrams(["abc", "bca", "cab", "acb"]));

// Duplicate strings
console.log('With duplicates:', groupAnagrams(["abc", "abc", "bca"]));

console.log('\n=== Performance Notes ===');
console.log('Time Complexity: O(n * k * log k)');
console.log('Space Complexity: O(n * k)');
console.log('- Efficient for most practical use cases');
console.log('- Uses Map for O(1) average lookup time');
console.log('- Sorting approach is intuitive and reliable');
