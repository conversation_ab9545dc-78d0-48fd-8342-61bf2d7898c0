const countFrequencies = require('./count-frequencies');

/**
 * Test suite for countFrequencies function
 */

function runTests() {
    console.log('Running tests for countFrequencies function...\n');
    
    let testsPassed = 0;
    let totalTests = 0;

    function test(description, input, expected) {
        totalTests++;
        const result = countFrequencies(input);
        const passed = JSON.stringify(result) === JSON.stringify(expected);
        
        console.log(`Test ${totalTests}: ${description}`);
        console.log(`  Input: [${input.join(', ')}]`);
        console.log(`  Expected: ${JSON.stringify(expected)}`);
        console.log(`  Got: ${JSON.stringify(result)}`);
        console.log(`  Status: ${passed ? '✅ PASSED' : '❌ FAILED'}\n`);
        
        if (passed) testsPassed++;
    }

    // Test cases
    test('Basic frequency counting', [1, 2, 2, 3, 3, 3], {1: 1, 2: 2, 3: 3});
    test('All same numbers', [1, 1, 1, 1], {1: 4});
    test('All unique numbers', [1, 2, 3, 4, 5], {1: 1, 2: 1, 3: 1, 4: 1, 5: 1});
    test('Empty array', [], {});
    test('Array with negative numbers and zero', [0, -1, -1, 0, 5], {0: 2, '-1': 2, 5: 1});
    test('Single element', [42], {42: 1});
    test('Large numbers', [1000, 1000, 2000], {1000: 2, 2000: 1});

    console.log(`\nTest Results: ${testsPassed}/${totalTests} tests passed`);
    
    if (testsPassed === totalTests) {
        console.log('🎉 All tests passed!');
    } else {
        console.log('❌ Some tests failed. Please review the implementation.');
    }
}

// Run the tests
runTests();
