# Group Anagrams Problem

## Problem Description
Given an array of strings `strs`, group the anagrams together. You can return the answer in any order.

**Example:**
- Input: `["eat","tea","tan","ate","nat","bat"]`
- Output: `[["eat","tea","ate"],["tan","nat"],["bat"]]`

## Solutions

### Approach 1: Sorting Characters (Primary Solution)
```javascript
function groupAnagrams(strs) {
    const anagramMap = new Map();
    
    for (const str of strs) {
        const sortedKey = str.split('').sort().join('');
        
        if (!anagramMap.has(sortedKey)) {
            anagramMap.set(sortedKey, []);
        }
        
        anagramMap.get(sortedKey).push(str);
    }
    
    return Array.from(anagramMap.values());
}
```

**How it works:**
1. For each string, sort its characters to create a unique key
2. Anagrams will have identical sorted character sequences
3. Group strings by their sorted keys
4. Return all groups as an array

**Time Complexity:** O(n × k × log k)
- n = number of strings
- k = maximum length of a string
- log k factor comes from sorting each string

**Space Complexity:** O(n × k)
- Storage for the hash map and result arrays

### Approach 2: Character Frequency Counting (Alternative)
```javascript
function groupAnagramsFrequency(strs) {
    const anagramMap = new Map();
    
    for (const str of strs) {
        const charCount = new Array(26).fill(0);
        
        for (const char of str) {
            charCount[char.charCodeAt(0) - 'a'.charCodeAt(0)]++;
        }
        
        const key = charCount.join(',');
        
        if (!anagramMap.has(key)) {
            anagramMap.set(key, []);
        }
        
        anagramMap.get(key).push(str);
    }
    
    return Array.from(anagramMap.values());
}
```

**How it works:**
1. Count frequency of each character (a-z) in each string
2. Use the frequency array as a unique key
3. Anagrams will have identical character frequencies
4. Group strings by their frequency signatures

**Time Complexity:** O(n × k)
- More efficient for very long strings since no sorting is needed

**Space Complexity:** O(n × k)
- Similar space usage but with fixed-size frequency arrays

## Files
- `group-anagrams.js` - Main implementations with basic tests
- `test-group-anagrams.js` - Comprehensive test suite with performance comparison
- `README.md` - This documentation

## Usage

### Running the main file:
```bash
node group-anagrams.js
```

### Running the test suite:
```bash
node test-group-anagrams.js
```

## Key Features
- ✅ **Readable**: Clear variable names and comments
- ✅ **Performant**: Efficient O(n × k × log k) time complexity
- ✅ **Robust**: Handles edge cases (empty arrays, single strings, etc.)
- ✅ **Two approaches**: Sorting vs frequency counting for different use cases
- ✅ **Well-tested**: Comprehensive test suite with multiple scenarios

## When to Use Each Approach
- **Sorting approach**: Better for general use, simpler to understand
- **Frequency approach**: Better for very long strings or when you need O(n × k) time complexity

## Test Cases Covered
- Basic anagram grouping
- Empty arrays
- Single strings
- No anagrams present
- All strings are anagrams
- Mixed cases with duplicates
- Single character strings
- Performance comparison between approaches
