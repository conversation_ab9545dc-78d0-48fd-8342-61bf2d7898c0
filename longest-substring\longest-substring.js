/**
 * Returns the length of the longest substring without repeating characters
 * Uses sliding window technique with a Set to track characters
 * 
 * @param {string} s - Input string
 * @returns {number} - Length of longest substring without repeating characters
 */
function lengthOfLongestSubstring(s) {
    if (!s || s.length === 0) {
        return 0;
    }
    
    let maxLength = 0;
    let left = 0;
    const charSet = new Set();
    
    for (let right = 0; right < s.length; right++) {
        // If character is already in the set, shrink window from left
        while (charSet.has(s[right])) {
            charSet.delete(s[left]);
            left++;
        }
        
        // Add current character to set
        charSet.add(s[right]);
        
        // Update max length
        maxLength = Math.max(maxLength, right - left + 1);
    }
    
    return maxLength;
}

// Test cases
console.log('Testing lengthOfLongestSubstring function:');
console.log('Input: "abcabcbb" -> Output:', lengthOfLongestSubstring("abcabcbb")); // Expected: 3
console.log('Input: "bbbbb" -> Output:', lengthOfLongestSubstring("bbbbb"));       // Expected: 1
console.log('Input: "pwwkew" -> Output:', lengthOfLongestSubstring("pwwkew"));     // Expected: 3
console.log('Input: "" -> Output:', lengthOfLongestSubstring(""));                // Expected: 0
console.log('Input: "abcdef" -> Output:', lengthOfLongestSubstring("abcdef"));     // Expected: 6
console.log('Input: "a" -> Output:', lengthOfLongestSubstring("a"));              // Expected: 1

module.exports = lengthOfLongestSubstring;
