# Frequency Counter Problem

## Problem Description
Given an array of integers, return an object where the keys are the numbers and the values are how often they appear.

## Solution
The solution uses a simple frequency map approach:
1. Initialize an empty object to store frequencies
2. Iterate through the array
3. For each number, increment its count in the frequency map
4. Return the frequency map

## Files
- `count-frequencies.js` - Main implementation with basic tests
- `test-count-frequencies.js` - Comprehensive test suite
- `README.md` - This documentation file

## Usage

### Running the main file:
```bash
node count-frequencies.js
```

### Running the test suite:
```bash
node test-count-frequencies.js
```

## Example
```javascript
const result = countFrequencies([1, 2, 2, 3, 3, 3]);
console.log(result); // Output: {1: 1, 2: 2, 3: 3}
```

## Time Complexity
- **Time**: O(n) where n is the length of the input array
- **Space**: O(k) where k is the number of unique elements in the array

## Test Cases Covered
- Basic frequency counting
- All same numbers
- All unique numbers
- Empty array
- Negative numbers and zero
- Single element
- Large numbers
