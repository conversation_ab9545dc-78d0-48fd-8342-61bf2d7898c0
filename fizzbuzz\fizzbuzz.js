'use strict';

process.stdin.resume();
process.stdin.setEncoding('utf-8');

let inputString = '';
let currentLine = 0;

process.stdin.on('data', function(inputStdin) {
    inputString += inputStdin;
});

process.stdin.on('end', function() {
    inputString = inputString.split('\n');

    main();
});

function readLine() {
    return inputString[currentLine++];
}

/*
 * FizzBuzz Problem
 * 
 * Given a number n, for each integer i in the range from 1 to n inclusive, 
 * print one value per line as follows:
 * - If i is a multiple of both 3 and 5, print "FizzBuzz"
 * - If i is a multiple of 3 (but not 5), print "Fizz"
 * - If i is a multiple of 5 (but not 3), print "Buzz"
 * - If i is not a multiple of 3 or 5, print the value of i
 * 
 * Time Complexity: O(n) - iterate through numbers 1 to n
 * Space Complexity: O(1) - constant space usage
 */

function fizzBuzz(n) {
    // Iterate through numbers from 1 to n inclusive
    for (let i = 1; i <= n; i++) {
        // Check if divisible by both 3 and 5 (i.e., divisible by 15)
        if (i % 15 === 0) {
            console.log("FizzBuzz");
        }
        // Check if divisible by 3 only
        else if (i % 3 === 0) {
            console.log("Fizz");
        }
        // Check if divisible by 5 only
        else if (i % 5 === 0) {
            console.log("Buzz");
        }
        // Not divisible by 3 or 5, print the number
        else {
            console.log(i);
        }
    }
}

function main() {
    const n = parseInt(readLine().trim(), 10);
    fizzBuzz(n);
}
