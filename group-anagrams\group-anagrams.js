/**
 * Group Anagrams Problem
 * Given an array of strings strs, group the anagrams together. 
 * You can return the answer in any order.
 * 
 * Time Complexity: O(n * k * log k) where n is the number of strings and k is the maximum length of a string
 * Space Complexity: O(n * k) for storing the grouped anagrams
 */

function groupAnagrams(strs) {
    // Use a Map to group anagrams by their sorted character signature
    const anagramMap = new Map();
    
    for (const str of strs) {
        // Sort the characters to create a unique key for anagrams
        // Anagrams will have the same sorted character sequence
        const sortedKey = str.split('').sort().join('');
        
        // If this key doesn't exist, create a new array
        if (!anagramMap.has(sortedKey)) {
            anagramMap.set(sortedKey, []);
        }
        
        // Add the current string to the appropriate group
        anagramMap.get(sortedKey).push(str);
    }
    
    // Convert Map values to array and return
    return Array.from(anagramMap.values());
}

// Alternative implementation using character frequency counting
// This can be more efficient for very long strings
function groupAnagramsFrequency(strs) {
    const anagramMap = new Map();
    
    for (const str of strs) {
        // Create a frequency signature using character counts
        const charCount = new Array(26).fill(0);
        
        for (const char of str) {
            charCount[char.charCodeAt(0) - 'a'.charCodeAt(0)]++;
        }
        
        // Use the frequency array as a key (convert to string)
        const key = charCount.join(',');
        
        if (!anagramMap.has(key)) {
            anagramMap.set(key, []);
        }
        
        anagramMap.get(key).push(str);
    }
    
    return Array.from(anagramMap.values());
}

// Test cases
console.log('Testing groupAnagrams function:');
console.log('Input: ["eat","tea","tan","ate","nat","bat"]');
console.log('Output:', groupAnagrams(["eat","tea","tan","ate","nat","bat"]));
console.log('Expected: [["eat","tea","ate"],["tan","nat"],["bat"]]');

console.log('\nAdditional test cases:');
console.log('Empty array:', groupAnagrams([]));
console.log('Single string:', groupAnagrams(["a"]));
console.log('No anagrams:', groupAnagrams(["abc", "def", "ghi"]));
console.log('All anagrams:', groupAnagrams(["abc", "bca", "cab"]));

module.exports = { groupAnagrams, groupAnagramsFrequency };
