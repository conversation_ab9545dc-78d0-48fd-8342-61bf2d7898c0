import React, { useState, useEffect } from 'react';

/**
 * Returns the length of the longest substring without repeating characters
 * Uses sliding window technique with a Set to track characters
 */
function lengthOfLongestSubstring(s) {
    if (!s || s.length === 0) {
        return 0;
    }
    
    let maxLength = 0;
    let left = 0;
    const charSet = new Set();
    
    for (let right = 0; right < s.length; right++) {
        // If character is already in the set, shrink window from left
        while (charSet.has(s[right])) {
            charSet.delete(s[left]);
            left++;
        }
        
        // Add current character to set
        charSet.add(s[right]);
        
        // Update max length
        maxLength = Math.max(maxLength, right - left + 1);
    }
    
    return maxLength;
}

/**
 * Helper function to find the actual longest substring (for demonstration)
 */
function findLongestSubstring(s) {
    if (!s || s.length === 0) {
        return '';
    }
    
    let maxLength = 0;
    let maxStart = 0;
    let left = 0;
    const charSet = new Set();
    
    for (let right = 0; right < s.length; right++) {
        while (charSet.has(s[right])) {
            charSet.delete(s[left]);
            left++;
        }
        
        charSet.add(s[right]);
        
        if (right - left + 1 > maxLength) {
            maxLength = right - left + 1;
            maxStart = left;
        }
    }
    
    return s.substring(maxStart, maxStart + maxLength);
}

const LongestSubstringComponent = () => {
    const [inputString, setInputString] = useState('abcabcbb');
    const [result, setResult] = useState(null);
    const [longestSubstring, setLongestSubstring] = useState('');

    const testCases = [
        { input: 'abcabcbb', expected: 3, description: 'Mixed repeating characters' },
        { input: 'bbbbb', expected: 1, description: 'All same characters' },
        { input: 'pwwkew', expected: 3, description: 'Complex pattern' },
        { input: '', expected: 0, description: 'Empty string' },
        { input: 'abcdef', expected: 6, description: 'No repeating characters' },
        { input: 'a', expected: 1, description: 'Single character' },
        { input: 'dvdf', expected: 3, description: 'Another test case' }
    ];

    useEffect(() => {
        const length = lengthOfLongestSubstring(inputString);
        const substring = findLongestSubstring(inputString);
        setResult(length);
        setLongestSubstring(substring);
    }, [inputString]);

    const handleTestCase = (testInput) => {
        setInputString(testInput);
    };

    return (
        <div style={{ 
            maxWidth: '800px', 
            margin: '0 auto', 
            padding: '20px',
            fontFamily: 'Arial, sans-serif'
        }}>
            <h1>Longest Substring Without Repeating Characters</h1>
            
            <div style={{ marginBottom: '20px' }}>
                <label htmlFor="input-string" style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    Enter a string:
                </label>
                <input
                    id="input-string"
                    type="text"
                    value={inputString}
                    onChange={(e) => setInputString(e.target.value)}
                    style={{
                        width: '100%',
                        padding: '10px',
                        fontSize: '16px',
                        border: '2px solid #ddd',
                        borderRadius: '4px',
                        marginBottom: '10px'
                    }}
                    placeholder="Enter a string to analyze..."
                />
            </div>

            {result !== null && (
                <div style={{
                    backgroundColor: '#f0f8ff',
                    padding: '15px',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #b0d4f1'
                }}>
                    <h3>Result:</h3>
                    <p><strong>Input:</strong> "{inputString}"</p>
                    <p><strong>Length of longest substring:</strong> {result}</p>
                    <p><strong>Longest substring:</strong> "{longestSubstring}"</p>
                </div>
            )}

            <div>
                <h3>Test Cases:</h3>
                <div style={{ display: 'grid', gap: '10px' }}>
                    {testCases.map((testCase, index) => {
                        const actualResult = lengthOfLongestSubstring(testCase.input);
                        const isCorrect = actualResult === testCase.expected;
                        
                        return (
                            <div
                                key={index}
                                style={{
                                    padding: '10px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px',
                                    backgroundColor: isCorrect ? '#e8f5e8' : '#ffe8e8',
                                    cursor: 'pointer'
                                }}
                                onClick={() => handleTestCase(testCase.input)}
                            >
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <div>
                                        <strong>Input:</strong> "{testCase.input}" 
                                        <span style={{ marginLeft: '10px', fontSize: '14px', color: '#666' }}>
                                            ({testCase.description})
                                        </span>
                                    </div>
                                    <div>
                                        <span style={{ marginRight: '10px' }}>
                                            Expected: {testCase.expected}, Got: {actualResult}
                                        </span>
                                        <span style={{ 
                                            color: isCorrect ? 'green' : 'red',
                                            fontWeight: 'bold'
                                        }}>
                                            {isCorrect ? '✓' : '✗'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>

            <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                <h3>Algorithm Explanation:</h3>
                <p>This solution uses the <strong>sliding window technique</strong>:</p>
                <ol>
                    <li>Use two pointers (left and right) to maintain a window</li>
                    <li>Use a Set to track characters in the current window</li>
                    <li>Expand the window by moving the right pointer</li>
                    <li>If a duplicate character is found, shrink the window from the left until the duplicate is removed</li>
                    <li>Keep track of the maximum window size encountered</li>
                </ol>
                <p><strong>Time Complexity:</strong> O(n) - each character is visited at most twice</p>
                <p><strong>Space Complexity:</strong> O(min(m,n)) - where m is the size of the character set</p>
            </div>
        </div>
    );
};

export default LongestSubstringComponent;
