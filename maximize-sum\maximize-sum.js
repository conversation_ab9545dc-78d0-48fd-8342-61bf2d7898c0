'use strict';

const fs = require('fs');

process.stdin.resume();
process.stdin.setEncoding('utf-8');

let inputString = '';
let currentLine = 0;

process.stdin.on('data', function(inputStdin) {
    inputString += inputStdin;
});

process.stdin.on('end', function() {
    inputString = inputString.split('\n');

    main();
});

function readLine() {
    return inputString[currentLine++];
}

/*
 * Maximize Sum Problem
 * 
 * Given an array and k sign-flip operations, find the maximum possible sum
 * after performing exactly k flips.
 * 
 * Strategy:
 * 1. First, flip all negative numbers to positive (if we have enough flips)
 * 2. If we still have remaining flips and they're odd, flip the smallest absolute value
 * 3. If remaining flips are even, we can flip any element twice (no net change)
 * 
 * Time Complexity: O(n log n) due to sorting
 * Space Complexity: O(n) for creating absolute value array
 */

function maximizeSum(arr, k) {
    const n = arr.length;
    
    // Count negative numbers
    let negativeCount = 0;
    for (let i = 0; i < n; i++) {
        if (arr[i] < 0) {
            negativeCount++;
        }
    }
    
    // Create array of absolute values and sort by magnitude
    const absArr = arr.map(x => Math.abs(x));
    absArr.sort((a, b) => a - b);
    
    // Calculate sum of all absolute values
    let maxSum = absArr.reduce((sum, val) => sum + val, 0);
    
    // Case 1: We have enough flips to make all numbers positive
    if (k >= negativeCount) {
        const remainingFlips = k - negativeCount;
        
        // If remaining flips is odd, we must flip one number
        // Choose the smallest absolute value to minimize loss
        if (remainingFlips % 2 === 1) {
            maxSum -= 2 * absArr[0]; // Subtract twice (from + to -)
        }
        // If remaining flips is even, we can flip any number twice (no net change)
    }
    // Case 2: We don't have enough flips to make all numbers positive
    else {
        // We can flip k negative numbers to positive
        // Choose the k largest absolute values among negatives
        
        // Sort original array by absolute value (descending for negatives we want to flip)
        const negatives = [];
        const positives = [];
        
        for (let i = 0; i < n; i++) {
            if (arr[i] < 0) {
                negatives.push(Math.abs(arr[i]));
            } else {
                positives.push(arr[i]);
            }
        }
        
        // Sort negatives by absolute value (descending) to flip largest ones first
        negatives.sort((a, b) => b - a);
        
        // Calculate sum: flip k largest negatives, keep rest as negative
        maxSum = 0;
        
        // Add flipped negatives (k largest)
        for (let i = 0; i < Math.min(k, negatives.length); i++) {
            maxSum += negatives[i];
        }
        
        // Add remaining negatives (as negative)
        for (let i = k; i < negatives.length; i++) {
            maxSum -= negatives[i];
        }
        
        // Add all positives
        for (let i = 0; i < positives.length; i++) {
            maxSum += positives[i];
        }
    }
    
    return maxSum;
}

function main() {
    const ws = fs.createWriteStream(process.env.OUTPUT_PATH || '/dev/stdout');

    const arrCount = parseInt(readLine().trim(), 10);

    let arr = [];

    for (let i = 0; i < arrCount; i++) {
        const arrItem = parseInt(readLine().trim(), 10);
        arr.push(arrItem);
    }

    const k = parseInt(readLine().trim(), 10);

    const result = maximizeSum(arr, k);

    ws.write(result + '\n');

    ws.end();
}

