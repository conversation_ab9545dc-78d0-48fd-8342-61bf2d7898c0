/*
 * Maximize Sum Problem - Test Version
 * 
 * Strategy:
 * 1. First, flip all negative numbers to positive (if we have enough flips)
 * 2. If we still have remaining flips and they're odd, flip the smallest absolute value
 * 3. If remaining flips are even, we can flip any element twice (no net change)
 */

function maximizeSum(arr, k) {
    const n = arr.length;
    
    // Count negative numbers
    let negativeCount = 0;
    for (let i = 0; i < n; i++) {
        if (arr[i] < 0) {
            negativeCount++;
        }
    }
    
    // Create array of absolute values and sort by magnitude
    const absArr = arr.map(x => Math.abs(x));
    absArr.sort((a, b) => a - b);
    
    // Calculate sum of all absolute values
    let maxSum = absArr.reduce((sum, val) => sum + val, 0);
    
    // Case 1: We have enough flips to make all numbers positive
    if (k >= negativeCount) {
        const remainingFlips = k - negativeCount;
        
        // If remaining flips is odd, we must flip one number
        // Choose the smallest absolute value to minimize loss
        if (remainingFlips % 2 === 1) {
            maxSum -= 2 * absArr[0]; // Subtract twice (from + to -)
        }
        // If remaining flips is even, we can flip any number twice (no net change)
    }
    // Case 2: We don't have enough flips to make all numbers positive
    else {
        // We can flip k negative numbers to positive
        // Choose the k largest absolute values among negatives
        
        const negatives = [];
        const positives = [];
        
        for (let i = 0; i < n; i++) {
            if (arr[i] < 0) {
                negatives.push(Math.abs(arr[i]));
            } else {
                positives.push(arr[i]);
            }
        }
        
        // Sort negatives by absolute value (descending) to flip largest ones first
        negatives.sort((a, b) => b - a);
        
        // Calculate sum: flip k largest negatives, keep rest as negative
        maxSum = 0;
        
        // Add flipped negatives (k largest)
        for (let i = 0; i < Math.min(k, negatives.length); i++) {
            maxSum += negatives[i];
        }
        
        // Add remaining negatives (as negative)
        for (let i = k; i < negatives.length; i++) {
            maxSum -= negatives[i];
        }
        
        // Add all positives
        for (let i = 0; i < positives.length; i++) {
            maxSum += positives[i];
        }
    }
    
    return maxSum;
}

// Test cases
console.log('=== Maximize Sum Test Cases ===');

// Test case 1: Example from problem
const test1_arr = [-5, -2, -3, 6, 7];
const test1_k = 3;
const test1_result = maximizeSum(test1_arr, test1_k);
console.log(`Test 1: arr=[${test1_arr}], k=${test1_k}`);
console.log(`Result: ${test1_result}, Expected: 23`);
console.log(`✅ ${test1_result === 23 ? 'PASSED' : 'FAILED'}`);

// Test case 2: Sample case 0
const test2_arr = [4, 2, 1, 9];
const test2_k = 1;
const test2_result = maximizeSum(test2_arr, test2_k);
console.log(`\nTest 2: arr=[${test2_arr}], k=${test2_k}`);
console.log(`Result: ${test2_result}, Expected: 14`);
console.log(`✅ ${test2_result === 14 ? 'PASSED' : 'FAILED'}`);

// Test case 3: Sample case 1
const test3_arr = [3, -2, -1, -3, 10];
const test3_k = 2;
const test3_result = maximizeSum(test3_arr, test3_k);
console.log(`\nTest 3: arr=[${test3_arr}], k=${test3_k}`);
console.log(`Result: ${test3_result}, Expected: 17`);
console.log(`✅ ${test3_result === 17 ? 'PASSED' : 'FAILED'}`);

// Test case 4: All negatives
const test4_arr = [-1, -2, -3, -4];
const test4_k = 2;
const test4_result = maximizeSum(test4_arr, test4_k);
console.log(`\nTest 4: arr=[${test4_arr}], k=${test4_k}`);
console.log(`Result: ${test4_result}, Expected: 4 (flip -4 and -3 to get 4+3-2-1=4)`);
console.log(`✅ ${test4_result === 4 ? 'PASSED' : 'FAILED'}`);

// Test case 5: Even remaining flips
const test5_arr = [-1, -2, 3];
const test5_k = 4;
const test5_result = maximizeSum(test5_arr, test5_k);
console.log(`\nTest 5: arr=[${test5_arr}], k=${test5_k}`);
console.log(`Result: ${test5_result}, Expected: 6 (flip both negatives, 2 flips left - even, so no change)`);
console.log(`✅ ${test5_result === 6 ? 'PASSED' : 'FAILED'}`);

console.log('\n=== Algorithm Explanation ===');
console.log('1. Count negative numbers in array');
console.log('2. If k >= negatives: flip all negatives, handle remaining flips');
console.log('   - If remaining flips odd: flip smallest absolute value');
console.log('   - If remaining flips even: no net change');
console.log('3. If k < negatives: flip k largest absolute value negatives');
console.log('4. Sum all resulting values');
