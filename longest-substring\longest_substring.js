/**
 * Returns the length of the longest substring without repeating characters
 * @param {string} s - The input string
 * @return {number} - Length of the longest substring without repeating characters
 */
function lengthOfLongestSubstring(s) {
    if (!s || s.length === 0) {
        return 0;
    }
    
    let maxLength = 0;
    let left = 0;
    const charSet = new Set();
    
    for (let right = 0; right < s.length; right++) {
        // If character is already in the set, shrink window from left
        while (charSet.has(s[right])) {
            charSet.delete(s[left]);
            left++;
        }
        
        // Add current character to set
        charSet.add(s[right]);
        
        // Update max length
        maxLength = Math.max(maxLength, right - left + 1);
    }
    
    return maxLength;
}

// Test cases
console.log("Test 1:");
console.log(`Input: "abcabcbb"`);
console.log(`Output: ${lengthOfLongestSubstring("abcabcbb")}`);
console.log(`Expected: 3\n`);

console.log("Test 2:");
console.log(`Input: "bbbbb"`);
console.log(`Output: ${lengthOfLongestSubstring("bbbbb")}`);
console.log(`Expected: 1\n`);

console.log("Test 3:");
console.log(`Input: "pwwkew"`);
console.log(`Output: ${lengthOfLongestSubstring("pwwkew")}`);
console.log(`Expected: 3\n`);

// Additional test cases
console.log("Additional Test Cases:");
console.log(`Empty string: ${lengthOfLongestSubstring("")}`); // Expected: 0
console.log(`Single character: ${lengthOfLongestSubstring("a")}`); // Expected: 1
console.log(`All unique: ${lengthOfLongestSubstring("abcdef")}`); // Expected: 6
console.log(`Spaces: ${lengthOfLongestSubstring("a b c a")}`); // Expected: 3

module.exports = lengthOfLongestSubstring;
