/**
 * Account Merge Problem
 * 
 * Given a list of accounts where each element is a list of strings:
 * - First string: person's name
 * - Rest: emails
 * 
 * If two accounts share an email, they belong to the same person.
 * Return merged accounts with sorted email lists.
 * 
 * Time Complexity: O(N * K * log(K)) where N = accounts, K = max emails per account
 * Space Complexity: O(N * K) for graph storage and result
 * 
 * Approach: Graph + DFS
 * 1. Build graph: emails as nodes, connect if in same account
 * 2. DFS to find connected components (merged accounts)
 * 3. Sort emails and retain original name
 */

function accountsMerge(accounts) {
    if (!accounts || accounts.length === 0) return [];
    
    // Build graph and email-to-name mapping
    const graph = new Map(); // email -> Set of connected emails
    const emailToName = new Map(); // email -> person's name
    
    // Step 1: Build the graph
    for (const account of accounts) {
        const name = account[0];
        const emails = account.slice(1);
        
        // Initialize graph nodes for all emails
        for (const email of emails) {
            if (!graph.has(email)) {
                graph.set(email, new Set());
            }
            emailToName.set(email, name);
        }
        
        // Connect all emails in this account (create edges)
        for (let i = 0; i < emails.length; i++) {
            for (let j = i + 1; j < emails.length; j++) {
                const email1 = emails[i];
                const email2 = emails[j];
                
                // Bidirectional connection
                graph.get(email1).add(email2);
                graph.get(email2).add(email1);
            }
        }
    }
    
    // Step 2: DFS to find connected components
    const visited = new Set();
    const result = [];
    
    for (const email of graph.keys()) {
        if (!visited.has(email)) {
            const component = [];
            dfs(email, graph, visited, component);
            
            // Sort emails and add name at the beginning
            component.sort();
            const name = emailToName.get(email);
            result.push([name, ...component]);
        }
    }
    
    return result;
}

// DFS helper function to collect connected emails
function dfs(email, graph, visited, component) {
    visited.add(email);
    component.push(email);
    
    // Visit all connected emails
    for (const neighbor of graph.get(email)) {
        if (!visited.has(neighbor)) {
            dfs(neighbor, graph, visited, component);
        }
    }
}

// Alternative approach using Union-Find (Disjoint Set Union)
class UnionFind {
    constructor() {
        this.parent = new Map();
        this.rank = new Map();
    }
    
    find(x) {
        if (!this.parent.has(x)) {
            this.parent.set(x, x);
            this.rank.set(x, 0);
        }
        
        if (this.parent.get(x) !== x) {
            this.parent.set(x, this.find(this.parent.get(x))); // Path compression
        }
        
        return this.parent.get(x);
    }
    
    union(x, y) {
        const rootX = this.find(x);
        const rootY = this.find(y);
        
        if (rootX !== rootY) {
            // Union by rank
            if (this.rank.get(rootX) < this.rank.get(rootY)) {
                this.parent.set(rootX, rootY);
            } else if (this.rank.get(rootX) > this.rank.get(rootY)) {
                this.parent.set(rootY, rootX);
            } else {
                this.parent.set(rootY, rootX);
                this.rank.set(rootX, this.rank.get(rootX) + 1);
            }
        }
    }
}

function accountsMergeUnionFind(accounts) {
    if (!accounts || accounts.length === 0) return [];
    
    const uf = new UnionFind();
    const emailToName = new Map();
    
    // Step 1: Union emails within each account
    for (const account of accounts) {
        const name = account[0];
        const emails = account.slice(1);
        
        for (const email of emails) {
            emailToName.set(email, name);
            
            // Union current email with first email in the account
            if (emails.length > 1) {
                uf.union(emails[0], email);
            }
        }
    }
    
    // Step 2: Group emails by their root parent
    const groups = new Map();
    
    for (const email of emailToName.keys()) {
        const root = uf.find(email);
        
        if (!groups.has(root)) {
            groups.set(root, []);
        }
        groups.get(root).push(email);
    }
    
    // Step 3: Build result with sorted emails
    const result = [];
    
    for (const emails of groups.values()) {
        emails.sort();
        const name = emailToName.get(emails[0]);
        result.push([name, ...emails]);
    }
    
    return result;
}

// ✅ Test the Code
console.log('=== Account Merge Test Cases ===');

const testCase1 = [
    ["John", "<EMAIL>", "<EMAIL>"],
    ["John", "<EMAIL>"],
    ["John", "<EMAIL>", "<EMAIL>"],
    ["Mary", "<EMAIL>"]
];

console.log('🧠 Approach 1: Graph + DFS');
console.log('Input:', JSON.stringify(testCase1, null, 2));
console.log('Output:', JSON.stringify(accountsMerge(testCase1), null, 2));

console.log('\n🧠 Approach 2: Union-Find');
console.log('Output:', JSON.stringify(accountsMergeUnionFind(testCase1), null, 2));

// Additional test cases
console.log('\n=== Additional Test Cases ===');

const testCase2 = [
    ["Alice", "<EMAIL>", "<EMAIL>"],
    ["Alice", "<EMAIL>", "<EMAIL>"]
];

console.log('Test 2 (DFS):', JSON.stringify(accountsMerge(testCase2), null, 2));

const testCase3 = [
    ["Bob", "<EMAIL>"],
    ["Bob", "<EMAIL>"],
    ["Bob", "<EMAIL>"]
];

console.log('Test 3 (DFS):', JSON.stringify(accountsMerge(testCase3), null, 2));

console.log('\n=== Complexity Analysis ===');
console.log('📊 Time & Space Complexity');
console.log('Graph + DFS Approach:');
console.log('  Time:  O(N * K²) for building graph + O(N * K * log K) for sorting');
console.log('  Space: O(N * K) for graph storage and result');
console.log('  Where: N = number of accounts, K = max emails per account');

console.log('\nUnion-Find Approach:');
console.log('  Time:  O(N * K * α(N * K)) + O(N * K * log K) for sorting');
console.log('  Space: O(N * K) for union-find structure and result');
console.log('  Where: α is the inverse Ackermann function (nearly constant)');

console.log('\n🎯 Key Insights:');
console.log('1. Model as graph problem: emails = nodes, shared accounts = edges');
console.log('2. Connected components represent merged accounts');
console.log('3. DFS/Union-Find both work well for finding components');
console.log('4. Remember to sort emails and preserve original names');
