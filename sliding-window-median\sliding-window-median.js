/**
 * Sliding Window Median Problem
 * 
 * Given:
 * - An array nums
 * - An integer k (window size)
 * 
 * Return:
 * - An array where each element is the median of the window of size k as it slides across nums
 */

// 🧠 Approach 1: Brute-force (Simple but not optimal)
// Time Complexity: O(n * k log k) - slice + sort for each window
// Space Complexity: O(k) per window
function medianSlidingWindow(nums, k) {
    const result = [];

    for (let i = 0; i <= nums.length - k; i++) {
        const window = nums.slice(i, i + k).sort((a, b) => a - b);
        const mid = Math.floor(k / 2);

        if (k % 2 === 0) {
            result.push((window[mid - 1] + window[mid]) / 2);
        } else {
            result.push(window[mid]);
        }
    }

    return result;
}

// 🌟 Approach 2: Optimized with Heaps (Advanced)
// Time Complexity: O(n log k) - much better for large datasets
// Space Complexity: O(k) - for the heaps
class MedianFinder {
    constructor() {
        this.maxHeap = []; // Left half (smaller elements) - max heap
        this.minHeap = []; // Right half (larger elements) - min heap
    }

    // Helper functions for heap operations
    _maxHeapPush(val) {
        this.maxHeap.push(val);
        this.maxHeap.sort((a, b) => b - a); // Max heap: largest first
    }

    _minHeapPush(val) {
        this.minHeap.push(val);
        this.minHeap.sort((a, b) => a - b); // Min heap: smallest first
    }

    _maxHeapPop() {
        return this.maxHeap.shift();
    }

    _minHeapPop() {
        return this.minHeap.shift();
    }

    _removeFromMaxHeap(val) {
        const index = this.maxHeap.indexOf(val);
        if (index !== -1) {
            this.maxHeap.splice(index, 1);
            this.maxHeap.sort((a, b) => b - a);
        }
    }

    _removeFromMinHeap(val) {
        const index = this.minHeap.indexOf(val);
        if (index !== -1) {
            this.minHeap.splice(index, 1);
            this.minHeap.sort((a, b) => a - b);
        }
    }

    addNumber(num) {
        // Add to appropriate heap
        if (this.maxHeap.length === 0 || num <= this.maxHeap[0]) {
            this._maxHeapPush(num);
        } else {
            this._minHeapPush(num);
        }

        // Balance heaps
        this._balance();
    }

    removeNumber(num) {
        if (this.maxHeap.includes(num)) {
            this._removeFromMaxHeap(num);
        } else {
            this._removeFromMinHeap(num);
        }
        this._balance();
    }

    _balance() {
        // Ensure maxHeap has at most 1 more element than minHeap
        if (this.maxHeap.length > this.minHeap.length + 1) {
            this._minHeapPush(this._maxHeapPop());
        } else if (this.minHeap.length > this.maxHeap.length) {
            this._maxHeapPush(this._minHeapPop());
        }
    }

    findMedian() {
        if (this.maxHeap.length === this.minHeap.length) {
            return (this.maxHeap[0] + this.minHeap[0]) / 2;
        } else {
            return this.maxHeap[0];
        }
    }
}

function medianSlidingWindowOptimized(nums, k) {
    if (nums.length === 0 || k === 0) return [];
    
    const result = [];
    const medianFinder = new MedianFinder();
    
    // Initialize first window
    for (let i = 0; i < k; i++) {
        medianFinder.addNumber(nums[i]);
    }
    result.push(medianFinder.findMedian());
    
    // Slide the window
    for (let i = k; i < nums.length; i++) {
        // Remove the element going out of window
        medianFinder.removeNumber(nums[i - k]);
        // Add the new element
        medianFinder.addNumber(nums[i]);
        // Get median of current window
        result.push(medianFinder.findMedian());
    }
    
    return result;
}

// ✅ Test the Code
console.log('=== Sliding Window Median Test Cases ===');

console.log('🧠 Approach 1: Brute-force');
console.log('Test 1:', medianSlidingWindow([1, 3, -1, -3, 5, 3, 6, 7], 3)); // [1, -1, -1, 3, 5, 6]
console.log('Test 2:', medianSlidingWindow([1, 2], 1));                     // [1, 2]
console.log('Test 3:', medianSlidingWindow([1, 2, 3, 4], 4));               // [2.5]
console.log('Test 4:', medianSlidingWindow([], 0));                        // []

console.log('\n🌟 Approach 2: Optimized with Heaps');
console.log('Test 1:', medianSlidingWindowOptimized([1, 3, -1, -3, 5, 3, 6, 7], 3)); // [1, -1, -1, 3, 5, 6]
console.log('Test 2:', medianSlidingWindowOptimized([1, 2], 1));                     // [1, 2]
console.log('Test 3:', medianSlidingWindowOptimized([1, 2, 3, 4], 4));               // [2.5]
console.log('Test 4:', medianSlidingWindowOptimized([], 0));                        // []

console.log('\n=== Performance Comparison ===');
console.log('📊 Time & Space Complexity');
console.log('Approach 1 (Brute-force):');
console.log('  Time:  O(n * k log k) → slice + sort for each window');
console.log('  Space: O(k) per window');
console.log('  Good for: small/medium data (n < 10⁴)');

console.log('\nApproach 2 (Heaps):');
console.log('  Time:  O(n log k) → much better for large datasets');
console.log('  Space: O(k) → for the heaps');
console.log('  Good for: large data (n > 10⁴), FAANG interviews');

console.log('\n🎯 Use Cases:');
console.log('- Small datasets: Use brute-force (simpler, easier to debug)');
console.log('- Large datasets: Use heaps (optimal performance)');
console.log('- Interviews: Know both approaches, start with brute-force then optimize');
