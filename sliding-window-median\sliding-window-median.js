/**
 * Sliding Window Median Problem
 * 
 * Given:
 * - An array nums
 * - An integer k (window size)
 * 
 * Return:
 * - An array where each element is the median of the window of size k as it slides across nums
 */

// Helper function to calculate median from sorted array
function getMedian(sortedArray) {
    const k = sortedArray.length;
    const mid = Math.floor(k / 2);

    if (k % 2 === 0) {
        return (sortedArray[mid - 1] + sortedArray[mid]) / 2;
    } else {
        return sortedArray[mid];
    }
}

// 🧠 Approach 1: Brute-force (Simple but not optimal)
// Time Complexity: O(n * k log k) - slice + sort for each window
// Space Complexity: O(k) per window
function medianSlidingWindow(nums, k) {
    if (nums.length === 0 || k === 0) return [];

    const result = [];

    for (let i = 0; i <= nums.length - k; i++) {
        const window = nums.slice(i, i + k).sort((a, b) => a - b);
        result.push(getMedian(window));
    }

    return result;
}

// 🧠 Approach 1.5: Improved Brute-force with Insertion Sort
// Time Complexity: O(n * k²) - better for small k, avoids full sort each time
// Space Complexity: O(k) - maintains sorted window
function medianSlidingWindowImproved(nums, k) {
    if (nums.length === 0 || k === 0) return [];

    const result = [];
    let sortedWindow = [];

    // Initialize first window with insertion sort
    for (let i = 0; i < k; i++) {
        insertSorted(sortedWindow, nums[i]);
    }
    result.push(getMedian(sortedWindow));

    // Slide the window
    for (let i = k; i < nums.length; i++) {
        // Remove the element going out of window
        const outgoing = nums[i - k];
        const outIndex = sortedWindow.indexOf(outgoing);
        sortedWindow.splice(outIndex, 1);

        // Add the new element in sorted position
        insertSorted(sortedWindow, nums[i]);

        result.push(getMedian(sortedWindow));
    }

    return result;
}

// Helper function to insert element in sorted position
function insertSorted(arr, val) {
    let left = 0;
    let right = arr.length;

    // Binary search for insertion position
    while (left < right) {
        const mid = Math.floor((left + right) / 2);
        if (arr[mid] < val) {
            left = mid + 1;
        } else {
            right = mid;
        }
    }

    arr.splice(left, 0, val);
}

// 🌟 Approach 2: Optimized with Heaps (Advanced)
// Time Complexity: O(n log k) - much better for large datasets
// Space Complexity: O(k) - for the heaps
class MedianFinder {
    constructor() {
        this.maxHeap = []; // Left half (smaller elements) - max heap
        this.minHeap = []; // Right half (larger elements) - min heap
    }

    // Helper functions for heap operations
    _maxHeapPush(val) {
        this.maxHeap.push(val);
        this.maxHeap.sort((a, b) => b - a); // Max heap: largest first
    }

    _minHeapPush(val) {
        this.minHeap.push(val);
        this.minHeap.sort((a, b) => a - b); // Min heap: smallest first
    }

    _maxHeapPop() {
        return this.maxHeap.shift();
    }

    _minHeapPop() {
        return this.minHeap.shift();
    }

    _removeFromMaxHeap(val) {
        const index = this.maxHeap.indexOf(val);
        if (index !== -1) {
            this.maxHeap.splice(index, 1);
            this.maxHeap.sort((a, b) => b - a);
        }
    }

    _removeFromMinHeap(val) {
        const index = this.minHeap.indexOf(val);
        if (index !== -1) {
            this.minHeap.splice(index, 1);
            this.minHeap.sort((a, b) => a - b);
        }
    }

    addNumber(num) {
        // Add to appropriate heap
        if (this.maxHeap.length === 0 || num <= this.maxHeap[0]) {
            this._maxHeapPush(num);
        } else {
            this._minHeapPush(num);
        }

        // Balance heaps
        this._balance();
    }

    removeNumber(num) {
        if (this.maxHeap.includes(num)) {
            this._removeFromMaxHeap(num);
        } else {
            this._removeFromMinHeap(num);
        }
        this._balance();
    }

    _balance() {
        // Ensure maxHeap has at most 1 more element than minHeap
        if (this.maxHeap.length > this.minHeap.length + 1) {
            this._minHeapPush(this._maxHeapPop());
        } else if (this.minHeap.length > this.maxHeap.length) {
            this._maxHeapPush(this._minHeapPop());
        }
    }

    findMedian() {
        if (this.maxHeap.length === this.minHeap.length) {
            return (this.maxHeap[0] + this.minHeap[0]) / 2;
        } else {
            return this.maxHeap[0];
        }
    }
}

function medianSlidingWindowOptimized(nums, k) {
    if (nums.length === 0 || k === 0) return [];
    
    const result = [];
    const medianFinder = new MedianFinder();
    
    // Initialize first window
    for (let i = 0; i < k; i++) {
        medianFinder.addNumber(nums[i]);
    }
    result.push(medianFinder.findMedian());
    
    // Slide the window
    for (let i = k; i < nums.length; i++) {
        // Remove the element going out of window
        medianFinder.removeNumber(nums[i - k]);
        // Add the new element
        medianFinder.addNumber(nums[i]);
        // Get median of current window
        result.push(medianFinder.findMedian());
    }
    
    return result;
}

// ✅ Test the Code
console.log('=== Sliding Window Median Test Cases ===');

console.log('🧠 Approach 1: Brute-force (Original)');
console.log('Test 1:', medianSlidingWindow([1, 3, -1, -3, 5, 3, 6, 7], 3)); // [1, -1, -1, 3, 5, 6]
console.log('Test 2:', medianSlidingWindow([1, 2], 1));                     // [1, 2]
console.log('Test 3:', medianSlidingWindow([1, 2, 3, 4], 4));               // [2.5]
console.log('Test 4:', medianSlidingWindow([], 0));                        // []

console.log('\n🧠 Approach 1.5: Improved Brute-force (Maintains sorted window)');
console.log('Test 1:', medianSlidingWindowImproved([1, 3, -1, -3, 5, 3, 6, 7], 3)); // [1, -1, -1, 3, 5, 6]
console.log('Test 2:', medianSlidingWindowImproved([1, 2], 1));                     // [1, 2]
console.log('Test 3:', medianSlidingWindowImproved([1, 2, 3, 4], 4));               // [2.5]
console.log('Test 4:', medianSlidingWindowImproved([], 0));                        // []

console.log('\n🌟 Approach 2: Optimized with Heaps');
console.log('Test 1:', medianSlidingWindowOptimized([1, 3, -1, -3, 5, 3, 6, 7], 3)); // [1, -1, -1, 3, 5, 6]
console.log('Test 2:', medianSlidingWindowOptimized([1, 2], 1));                     // [1, 2]
console.log('Test 3:', medianSlidingWindowOptimized([1, 2, 3, 4], 4));               // [2.5]
console.log('Test 4:', medianSlidingWindowOptimized([], 0));                        // []

console.log('\n=== Performance Comparison ===');
console.log('📊 Time & Space Complexity');

console.log('Approach 1 (Original Brute-force):');
console.log('  Time:  O(n * k log k) → slice + sort for each window');
console.log('  Space: O(k) per window');
console.log('  Issues: ❗ Inefficient sorting per window, repetitive median calc');

console.log('\nApproach 1.5 (Improved Brute-force):');
console.log('  Time:  O(n * k²) → better for small k, maintains sorted window');
console.log('  Space: O(k) → single sorted window maintained');
console.log('  Fixes: ✅ Avoids full sort, uses binary search insertion');
console.log('  Good for: small k (k < 100), easier to understand than heaps');

console.log('\nApproach 2 (Heaps - Optimal):');
console.log('  Time:  O(n log k) → best for large datasets');
console.log('  Space: O(k) → for the heaps');
console.log('  Good for: large data (n > 10⁴), FAANG interviews');

console.log('\n🎯 Use Cases & Strategy:');
console.log('- Learning/Small k: Use improved brute-force (cleaner, easier to debug)');
console.log('- Medium datasets: Choose based on k size (k² vs k log k)');
console.log('- Large datasets: Use heaps (optimal performance)');
console.log('- Interviews: Start with improved brute-force, then optimize to heaps');

console.log('\n✅ Key Improvements Made:');
console.log('1. Abstracted median calculation into helper function');
console.log('2. Added improved approach that maintains sorted window');
console.log('3. Uses binary search for efficient insertion');
console.log('4. Avoids redundant sorting on each window slide');
